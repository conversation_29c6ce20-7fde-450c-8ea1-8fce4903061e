from fastapi import Request, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, Annotated
import logging

from app.database import get_db, set_tenant_context, clear_tenant_context
from app.models.user import User, TenantUser
from app.utils.security import verify_token
from app.utils.permissions import check_permission, Permission

logger = logging.getLogger(__name__)

security = HTTPBearer()


class AuthMiddleware:
    """Authentication and authorization middleware"""
    
    def __init__(self):
        self.security = HTTPBearer(auto_error=False)
    
    async def __call__(self, request: Request, call_next):
        """Process request through auth middleware"""
        response = await call_next(request)
        return response


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """Get current authenticated user"""
    if not credentials:
        return None
    
    token_data = verify_token(credentials.credentials, "access")
    if not token_data:
        return None
    
    user_id = token_data.get("sub")
    if not user_id:
        return None
    
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    return user


def get_current_active_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get current active user or raise exception"""
    user = get_current_user(credentials, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user


def get_current_tenant_user(
    request: Request,
    user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> TenantUser:
    """Get current tenant user context"""
    # Get tenant ID from header
    tenant_id = request.headers.get("X-Tenant-ID")
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant ID header is required"
        )

    # Find user's membership in this tenant
    tenant_user = db.query(TenantUser).filter(
        TenantUser.user_id == user.id,
        TenantUser.tenant_id == tenant_id,
        TenantUser.is_active == True
    ).first()

    if not tenant_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User does not have access to this tenant"
        )

    # Set tenant context for RLS
    set_tenant_context(db, tenant_id)

    return tenant_user


def require_permission(permission: Permission):
    """Decorator to require specific permission"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get tenant_user from kwargs (should be injected by dependency)
            tenant_user = kwargs.get('tenant_user')
            if not tenant_user:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Tenant user context not found"
                )
            
            # Check permission
            if not check_permission(tenant_user.role.permissions, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission {permission.value} required"
                )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def verify_temp_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify temporary token for 2FA flow"""
    return verify_token(token, "temp")


def get_user_from_temp_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get user from temporary token"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Temporary token required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token_data = verify_temp_token(credentials.credentials)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired temporary token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id = token_data.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user
