import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { ArrowLeftIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { actionItemsApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';
import toast from 'react-hot-toast';
import '../appCustomStyles.css';

interface InvoiceLineAccount {
  line_description: string;
  account_code: string;
  account_name?: string | null;
  amount?: number;
}

interface InvoicePreviewProps {
  fileData: string;
  fileType: string;
}





function InvoicePreview({ fileData, fileType }: InvoicePreviewProps) {
  if (!fileData) {
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">File Preview</h3>
        <p className="text-sm text-gray-500">No file data available</p>
      </div>
    );
  }

  const renderPreview = () => {

    if (fileType === 'pdf') {
      // For PDF files, create a data URL and embed
      const pdfDataUrl = `data:application/pdf;base64,${fileData}`;
      return (
        <div className="w-full h-96 border rounded">
          <embed
            src={pdfDataUrl}
            type="application/pdf"
            width="100%"
            height="100%"
            className="rounded"
          />
        </div>
      );
    } else if (['png', 'jpg', 'jpeg'].includes(fileType?.toLowerCase())) {
      // For image files, create a data URL and display as img
      const imageDataUrl = `data:image/${fileType};base64,${fileData}`;
      return (
        <div className="w-full flex justify-center">
          <img
            src={imageDataUrl}
            alt="Invoice preview"
            className="max-w-full max-h-96 object-contain rounded border"
          />
        </div>
      );
    } else {
      return (
        <p className="text-sm text-gray-500">
          Preview not available for file type: {fileType}
        </p>
      );
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h3 className="font-medium text-gray-900 mb-4">File Preview</h3>
      <p className="text-sm text-gray-500 mb-4">
        File type: {fileType || 'Unknown'}
      </p>
      {renderPreview()}
    </div>
  );
}

export default function ActionItemDetailsPage() {
  const { actionItemId } = useParams<{ actionItemId: string }>();
  const navigate = useNavigate();
  const { currentTenant } = useTenant();
  const queryClient = useQueryClient();
  
  const [lineAccounts, setLineAccounts] = useState<InvoiceLineAccount[]>([]);
  const [resolutionNotes, setResolutionNotes] = useState('');

  const { data: actionItemDetails, isLoading, error } = useQuery(
    ['actionItemDetails', actionItemId],
    () => actionItemsApi.getActionItemDetails(actionItemId!),
    {
      enabled: !!actionItemId && !!currentTenant,
      onSuccess: (data) => {
        // Prefer real invoice rows from invoice.raw_data.invoice_rows; fallback to extracted_lines
        const rawRows = data?.invoice?.raw_data?.invoice_rows;
        if (Array.isArray(rawRows) && rawRows.length > 0) {
          const parsed = rawRows.map((row: any) => {
            const amountSrc = row?.row_amount ?? row?.total_price ?? row?.amount;
            const amount = typeof amountSrc === 'string' ? parseFloat(String(amountSrc).replace(/[^0-9.,-]/g, '').replace(',', '.')) : amountSrc;
            return {
              line_description: row?.description ?? '',
              account_code: '',
              account_name: null,
              amount: isNaN(Number(amount)) ? undefined : Number(amount)
            } as InvoiceLineAccount;
          });
          setLineAccounts(parsed);
        } else if (data?.extracted_lines) {
          setLineAccounts(data.extracted_lines.map((line: any) => ({
            line_description: line.description,
            account_code: line.account_code || '',
            account_name: line.account_name || null,
            amount: line.amount
          })));
        }
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to load action item details');
      }
    }
  );

  const resolveMutation = useMutation(
    (data: { line_accounts: InvoiceLineAccount[]; resolution_notes?: string }) =>
      actionItemsApi.resolveActionItem(actionItemId!, data),
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries(['actionItems']);
        queryClient.invalidateQueries(['actionItemDetails']);
        toast.success('Action item resolved successfully!');
        if (response.processing_continued) {
          toast.success('Invoice processing continued automatically');
        }
        navigate('/action-items');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to resolve action item');
      },
    }
  );

  const handleAccountChange = (index: number, field: keyof InvoiceLineAccount, value: string | number) => {
    setLineAccounts(prev => prev.map((line, i) => 
      i === index ? { ...line, [field]: value } : line
    ));
  };

  const handleSubmit = () => {
    // Validate that all lines have account codes
    const invalidLines = lineAccounts.filter(line => !String(line.account_code || '').trim());
    if (invalidLines.length > 0) {
      toast.error('Please set account codes for all invoice lines');
      return;
    }

    resolveMutation.mutate({
      line_accounts: lineAccounts.map(line => ({
        ...line,
        account_name: line.account_name || ""
      })),
      resolution_notes: resolutionNotes.trim() || undefined
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="loading-container">
          <div className="loading-spinner"></div>
        </div>
      </div>
    );
  }

  if (error || !actionItemDetails) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-red-600">Failed to load action item details</p>
            <button
              onClick={() => navigate('/action-items')}
              className="mt-4 btn-primary"
            >
              Back to Action Items
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/action-items')}
              className="btn-secondary"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Action Items
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              {actionItemDetails.action_item.title}
            </h1>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left side - Invoice Lines */}
          <div className="card-container">
            <h2 className="section-title">Invoice Lines & Account Assignment</h2>
            
            <div className="space-y-4">
              {lineAccounts.map((line, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4 border">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Line Description
                    </label>
                    <p className="text-sm text-gray-900 bg-white p-2 rounded border">
                      {line.line_description}
                    </p>
                  </div>
                  
                  <div className="flex items-end justify-between gap-3">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Bookkeeping account *
                      </label>
                      <input
                        type="text"
                        value={line.account_code}
                        onChange={(e) => handleAccountChange(index, 'account_code', e.target.value)}
                        className="input-custom"
                        placeholder="insert bookkeeping account"
                        required
                      />
                    </div>
                    <div className="text-right">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Amount
                      </label>
                      <p className="text-sm text-gray-900">
                        {line.amount !== undefined && line.amount !== null ? Number(line.amount).toFixed(2) : '-'} {actionItemDetails.invoice?.currency || 'kr'}
                      </p>
                    </div>
                  </div>


                </div>
              ))}
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Resolution Notes (Optional)
              </label>
              <textarea
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                className="input-field"
                rows={3}
                placeholder="Add any notes about this resolution..."
              />
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={handleSubmit}
                disabled={resolveMutation.isLoading}
                className="btn-primary"
              >
                <CheckCircleIcon className="h-5 w-5 mr-2" />
                {resolveMutation.isLoading ? 'Resolving...' : 'Approve & Continue Processing'}
              </button>
            </div>
          </div>

          {/* Right side - Invoice Preview */}
          <div className="card-container">
            <h2 className="section-title">Invoice Preview</h2>
            
            {actionItemDetails.invoice ? (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Invoice Information</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Supplier:</span>
                      <p className="font-medium">{actionItemDetails.invoice.supplier_name || 'Unknown'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Invoice Number:</span>
                      <p className="font-medium">{actionItemDetails.invoice.invoice_number || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Date:</span>
                      <p className="font-medium">{actionItemDetails.invoice.invoice_date || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Total Amount:</span>
                      <p className="font-medium">
                        {actionItemDetails.invoice.total_amount ? 
                          `${actionItemDetails.invoice.total_amount} ${actionItemDetails.invoice.currency || 'kr'}` : 
                          'N/A'
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Invoice file preview */}
                <InvoicePreview
                  fileData={actionItemDetails.invoice.file_data}
                  fileType={actionItemDetails.invoice.file_type}
                />
              </div>
            ) : (
              <p className="text-gray-500">No invoice information available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
