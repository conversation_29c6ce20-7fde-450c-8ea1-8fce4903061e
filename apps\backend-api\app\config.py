from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # Database
    database_url: str = "*************************************************/aggie_dev"
    postgres_db: Optional[str] = None
    postgres_user: Optional[str] = None
    postgres_password: Optional[str] = None

    # Redis
    redis_url: str = "redis://redis:6379/0"
    redis_password: Optional[str] = None
    
    # Security
    jwt_secret_key: str = "dev-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    encryption_key: str = "dev-encryption-key-32-chars-long"
    
    # LLM Configuration
    llm_provider: str = "openai"  # or "azure_openai"
    openai_api_key: Optional[str] = None
    azure_openai_api_key: Optional[str] = None
    azure_openai_endpoint: Optional[str] = None
    azure_openai_deployment_name: str = "gpt-4o-mini"
    azure_openai_api_version: Optional[str] = None
    openai_model: str = "o4-mini"

    # OAuth2 Integration Settings
    fortnox_client_id: Optional[str] = None
    fortnox_client_secret: Optional[str] = None
    fortnox_api_url: str = "https://api.fortnox.se/3/"

    visma_client_id: Optional[str] = None
    visma_client_secret: Optional[str] = None
    visma_api_url: str = "https://eaccountingapi.vismaonline.com/v2/"
    
    # Email Configuration
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_from_email: Optional[str] = None
    
    # Application
    environment: str = "development"
    debug: bool = True
    frontend_api_url: Optional[str] = None
    use_nginx_proxy: bool = True  # Set to False when running backend directly without nginx
    
    # File Upload
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    upload_dir: str = "uploads"
    
    # Celery
    celery_broker_url: Optional[str] = None
    celery_result_backend: Optional[str] = None

    # Google Search API (for AI Agent web search)
    google_search_api_key: Optional[str] = None
    google_search_engine_id: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Build database URL from components if provided
        if self.postgres_db and self.postgres_user and self.postgres_password:
            from urllib.parse import quote_plus
            encoded_password = quote_plus(self.postgres_password)
            # Use 'db' as hostname when running in Docker, 'localhost' otherwise
            db_host = "db" if self.environment in ["development", "production"] else "localhost"
            self.database_url = f"postgresql://{self.postgres_user}:{encoded_password}@{db_host}:5432/{self.postgres_db}"

        # Build Redis URL with password if provided
        if self.redis_password:
            from urllib.parse import quote_plus
            encoded_redis_password = quote_plus(self.redis_password)
            # Use 'redis' as hostname when running in Docker, 'localhost' otherwise
            redis_host = "redis" if self.environment in ["development", "production"] else "localhost"
            self.redis_url = f"redis://:{encoded_redis_password}@{redis_host}:6379/0"

        # Set Celery URLs from Redis URL if not explicitly set
        if not self.celery_broker_url:
            self.celery_broker_url = self.redis_url
        if not self.celery_result_backend:
            self.celery_result_backend = self.redis_url


settings = Settings()


def get_encryption_key() -> bytes:
    """Get Fernet-compatible encryption key"""
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import base64

    # Use PBKDF2 to derive a proper Fernet key from the settings key
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=b'aggie_salt_2024',  # Fixed salt for consistency
        iterations=100000,
    )
    key_bytes = kdf.derive(settings.encryption_key.encode())
    # Return base64-encoded key as required by Fernet
    return base64.urlsafe_b64encode(key_bytes)
