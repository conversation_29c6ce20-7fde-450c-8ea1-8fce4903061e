# Development overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  backend-api:
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
      # Python development optimizations
      - PYTHONDONTWRITEBYTECODE=1  # Prevent .pyc files
      - PYTHONUNBUFFERED=1         # Better logging output
      - PYTHONPATH=/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app --log-level debug
    # Override user to root for development (easier file permissions)
    user: root

  worker:
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
      # Python development optimizations
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    # Override user to root for development
    user: root

  scheduler:
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
      # Python development optimizations
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    # Keep user as root (already set in main compose file)

  admin-frontend:
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      - PORT=3001
      # Hot reload optimizations
      - CHOKIDAR_USEPOLLING=true    # Better file watching in Docker
      - FAST_REFRESH=true           # Enable React Fast Refresh
      - WDS_SOCKET_HOST=localhost   # Better WebSocket connection
      - WDS_SOCKET_PORT=3001        # Explicit WebSocket port
      - GENERATE_SOURCEMAP=true     # Better debugging
    # Add development-specific volumes for better performance
    volumes:
      - ./apps/admin-frontend/src:/app/apps/admin-frontend/src
      - ./apps/admin-frontend/public:/app/apps/admin-frontend/public
      - ./packages:/app/packages
      - ./packages/shared-types:/app/apps/admin-frontend/node_modules/@aggie/shared-types
      - ./packages/ui-components:/app/apps/admin-frontend/node_modules/@aggie/ui-components
      # Exclude node_modules from host to prevent conflicts
      - /app/node_modules
      - /app/apps/admin-frontend/node_modules

  app-frontend:
    environment:
      - REACT_APP_API_URL=http://localhost:3002
      - PORT=3002
      # Hot reload optimizations
      - CHOKIDAR_USEPOLLING=true    # Better file watching in Docker
      - FAST_REFRESH=true           # Enable React Fast Refresh
      - WDS_SOCKET_HOST=localhost   # Better WebSocket connection
      - WDS_SOCKET_PORT=3002        # Explicit WebSocket port
      - GENERATE_SOURCEMAP=true     # Better debugging
    # Add development-specific volumes for better performance
    volumes:
      - ./apps/app-frontend/src:/app/apps/app-frontend/src
      - ./apps/app-frontend/public:/app/apps/app-frontend/public
      - ./packages:/app/packages
      - ./packages/shared-types:/app/apps/app-frontend/node_modules/@aggie/shared-types
      - ./packages/ui-components:/app/apps/app-frontend/node_modules/@aggie/ui-components
      # Exclude node_modules from host to prevent conflicts
      - /app/node_modules
      - /app/apps/app-frontend/node_modules

  # Development-specific service for easier debugging
  db:
    # Add development-specific logging
    command: postgres -c log_statement=all -c log_destination=stderr -c log_min_messages=info
    environment:
      POSTGRES_DB: aggie_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      # Enable more verbose logging for development
      POSTGRES_INITDB_ARGS: "--auth-host=trust"

  redis:
    # Add development-specific configuration
    command: redis-server --loglevel verbose --save ""
